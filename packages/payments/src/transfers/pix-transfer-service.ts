import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments/provider/factory";
import { calculateTransactionFees } from "../taxes/calculator";
import type { PixKeyType } from "@prisma/client";

export interface PixTransferRequest {
  amount: number;
  pixKey: string;
  pixKeyType: PixKeyType;
  organizationId: string;
  description?: string;
  gatewayType?: string;
  metadata?: Record<string, any>;
  // Internal fields
  userId?: string;
  requiresTwoFactor?: boolean;
}

export interface PixTransferResult {
  id: string;
  status: string;
  amount: number;
  pixKey: string;
  pixKeyType: PixKeyType;
  gatewayType: string;
  totalFee: number;
  totalAmount: number;
  externalId?: string;
  message?: string;
}

/**
 * Shared PIX transfer service that handles the complete transfer flow
 * Used by both API and web app endpoints to ensure consistency
 */
export class PixTransferService {
  /**
   * Process a PIX transfer request
   */
  static async processTransfer(request: PixTransferRequest): Promise<PixTransferResult> {
    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      description,
      gatewayType,
      metadata = {},
      userId,
      requiresTwoFactor = false
    } = request;

    logger.info("Processing PIX transfer request", {
      organizationId,
      amount,
      pixKeyType,
      requiresTwoFactor,
      userId
    });

    // Validate organization access and status
    await this.validateOrganization(organizationId);

    // Get the appropriate gateway
    const gateway = await this.getTransferGateway(organizationId, gatewayType);

    // Calculate fees using centralized system
    const feeCalculation = await calculateTransactionFees(
      organizationId,
      amount,
      'TRANSFER',
      gateway.id
    );

    const totalAmount = amount + feeCalculation.totalFee;

    logger.info("Fee calculation for PIX transfer", {
      organizationId,
      amount,
      feeCalculation,
      totalAmount,
      gatewayId: gateway.id
    });

    // Validate balance
    await this.validateBalance(organizationId, totalAmount);

    // Create transaction and reserve balance
    const transaction = await this.createTransactionAndReserveBalance({
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      description: description || `Transferência PIX para ${pixKeyType} ${pixKey}`,
      gateway,
      feeCalculation,
      totalAmount,
      metadata: {
        ...metadata,
        createdBy: userId,
        requiresTwoFactor,
        gatewayType: gateway.type,
        feeCalculation: {
          percentFee: feeCalculation.percentFee,
          fixedFee: feeCalculation.fixedFee,
          totalFee: feeCalculation.totalFee,
          transferAmount: amount,
          totalReserved: totalAmount,
          source: feeCalculation.source
        }
      }
    });

    // Process the transfer with the payment provider
    try {
      const transferResult = await this.processWithProvider(
        transaction,
        gateway,
        {
          amount,
          pixKey,
          pixKeyType,
          organizationId,
          description: description || `Transferência PIX para ${pixKeyType} ${pixKey}`
        }
      );

      return {
        id: transaction.id,
        status: transferResult.status || "PROCESSING",
        amount,
        pixKey,
        pixKeyType,
        gatewayType: gateway.type,
        totalFee: feeCalculation.totalFee,
        totalAmount,
        externalId: transferResult.externalId,
        message: transferResult.message || "Transfer processed successfully"
      };

    } catch (error) {
      // Handle transfer failure - unreserve balance
      await this.handleTransferFailure(transaction.id, totalAmount, organizationId, error);
      throw error;
    }
  }

  /**
   * Validate organization exists and is approved
   */
  private static async validateOrganization(organizationId: string): Promise<void> {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true, name: true }
    });

    if (!organization) {
      throw new Error("Organization not found");
    }

    if (organization.status !== "APPROVED") {
      throw new Error(`Organization is not approved: ${organization.status}`);
    }
  }

  /**
   * Get the appropriate gateway for transfers
   */
  private static async getTransferGateway(organizationId: string, preferredType?: string) {
    let gateway;

    // If a specific gateway type is requested, try to find it first
    if (preferredType) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          type: preferredType,
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        include: {
          organizations: {
            where: { organizationId }
          }
        }
      });
    }

    // If no specific type or not found, look for PLUGGOU_PIX (preferred for transfers)
    if (!gateway) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          type: "PLUGGOU_PIX",
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        include: {
          organizations: {
            where: { organizationId }
          }
        }
      });
    }

    // Fallback to any active gateway that can send
    if (!gateway) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' }
        ],
        include: {
          organizations: {
            where: { organizationId }
          }
        }
      });
    }

    // Global gateway fallback
    if (!gateway) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          isGlobal: true
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' }
        ]
      });
    }

    if (!gateway) {
      throw new Error("No active gateway available for transfers");
    }

    logger.info(`Using gateway for PIX transfer: ${gateway.type}`, {
      gatewayId: gateway.id,
      organizationId
    });

    return gateway;
  }

  /**
   * Validate organization has sufficient balance
   */
  private static async validateBalance(organizationId: string, requiredAmount: number): Promise<void> {
    const balance = await db.organizationBalance.findUnique({
      where: { organizationId }
    });

    if (!balance) {
      // Create zero balance record
      await db.organizationBalance.create({
        data: {
          organizationId,
          availableBalance: 0,
          pendingBalance: 0,
          reservedBalance: 0
        }
      });

      throw new Error("Insufficient balance for this transfer");
    }

    if (balance.availableBalance < requiredAmount) {
      throw new Error(
        `Insufficient balance: available ${balance.availableBalance}, required ${requiredAmount}`
      );
    }
  }

  /**
   * Create transaction record and reserve balance atomically
   */
  private static async createTransactionAndReserveBalance(params: {
    amount: number;
    pixKey: string;
    pixKeyType: PixKeyType;
    organizationId: string;
    description: string;
    gateway: any;
    feeCalculation: any;
    totalAmount: number;
    metadata: Record<string, any>;
  }) {
    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      description,
      gateway,
      feeCalculation,
      totalAmount,
      metadata
    } = params;

    return await db.$transaction(async (tx) => {
      // Create the transaction record
      const transaction = await tx.transaction.create({
        data: {
          amount,
          status: "PENDING",
          type: "SEND",
          description,
          pixKey,
          pixKeyType,
          customerName: "Transferência PIX",
          customerEmail: metadata.createdBy ? `user-${metadata.createdBy}@transfer.local` : "<EMAIL>",
          organizationId,
          gatewayId: gateway.id,
          gatewayName: gateway.name,
          // Store fee information in dedicated fields
          percentFee: feeCalculation.percentFee,
          fixedFee: feeCalculation.fixedFee,
          totalFee: feeCalculation.totalFee,
          netAmount: amount, // For transfers, netAmount is the transfer amount
          metadata
        }
      });

      // Reserve balance (deduct from available, add to reserved)
      const updatedBalance = await tx.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { decrement: totalAmount },
          reservedBalance: { increment: totalAmount }
        }
      });

      // Record balance history
      await tx.balanceHistory.create({
        data: {
          organizationId,
          transactionId: transaction.id,
          operation: "RESERVE",
          amount: totalAmount,
          description: `Reserva para transferência PIX: ${transaction.id} (valor: ${amount}, taxa: ${feeCalculation.totalFee})`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info("Transaction created and balance reserved", {
        transactionId: transaction.id,
        organizationId,
        amount,
        totalFee: feeCalculation.totalFee,
        totalReserved: totalAmount,
        availableBalance: updatedBalance.availableBalance
      });

      return transaction;
    });
  }

  /**
   * Process transfer with payment provider
   */
  private static async processWithProvider(
    transaction: any,
    gateway: any,
    transferData: {
      amount: number;
      pixKey: string;
      pixKeyType: PixKeyType;
      organizationId: string;
      description: string;
    }
  ) {
    // Get payment provider
    const paymentProvider = await getPaymentProvider(transferData.organizationId, {
      forceType: gateway.type,
      action: 'withdrawal'
    });

    logger.info(`Processing transfer with provider: ${gateway.type}`, {
      gatewayId: gateway.id,
      transactionId: transaction.id
    });

    // Update transaction status to PROCESSING
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: "PROCESSING",
        metadata: {
          ...(transaction.metadata as any || {}),
          processingStarted: new Date().toISOString()
        }
      }
    });

    // Process the withdrawal
    const postbackUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/${gateway.type.toLowerCase()}`;

    const withdrawResult = await paymentProvider.processPixWithdrawal({
      ...transferData,
      transactionId: transaction.id,
      postbackUrl,
      fee: transaction.totalFee || 0
    });

    // Update transaction with external ID and provider response
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        externalId: withdrawResult.id_envio || withdrawResult.id || withdrawResult.txid,
        metadata: {
          ...(transaction.metadata as any || {}),
          // Store essential identifiers
          id_envio: withdrawResult.id_envio,
          txid: withdrawResult.txid,
          id: withdrawResult.id,
          endToEndId: withdrawResult.endToEndId,
          // Store provider response
          providerResponse: withdrawResult.raw || withdrawResult,
          processingCompleted: new Date().toISOString()
        }
      }
    });

    return {
      status: withdrawResult.status || "PROCESSING",
      externalId: withdrawResult.id_envio || withdrawResult.id || withdrawResult.txid,
      message: "Transfer processed successfully"
    };
  }

  /**
   * Handle transfer failure by unreserving balance
   */
  private static async handleTransferFailure(
    transactionId: string,
    totalAmount: number,
    organizationId: string,
    error: any
  ): Promise<void> {
    logger.error("Handling transfer failure", {
      transactionId,
      organizationId,
      totalAmount,
      error: error instanceof Error ? error.message : "Unknown error"
    });

    try {
      // Update transaction status to CANCELED
      await db.transaction.update({
        where: { id: transactionId },
        data: {
          status: "CANCELED",
          metadata: {
            error: error instanceof Error ? error.message : "Unknown error",
            errorAt: new Date().toISOString(),
            status: "CANCELED"
          }
        }
      });

      // Unreserve balance (add back to available, remove from reserved)
      const updatedBalance = await db.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { increment: totalAmount },
          reservedBalance: { decrement: totalAmount }
        }
      });

      // Record balance history
      await db.balanceHistory.create({
        data: {
          organizationId,
          transactionId,
          operation: "UNRESERVE",
          amount: totalAmount,
          description: `Devolução de reserva por falha na transferência: ${transactionId}`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info("Balance unreserved after transfer failure", {
        transactionId,
        organizationId,
        amount: totalAmount
      });

    } catch (cleanupError) {
      logger.error("Error during transfer failure cleanup", {
        transactionId,
        organizationId,
        originalError: error instanceof Error ? error.message : "Unknown error",
        cleanupError: cleanupError instanceof Error ? cleanupError.message : "Unknown cleanup error"
      });
    }
  }
}
